# 编译输出目录
target/
dist/
build/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 依赖目录
node_modules/
.pnp
.pnp.js

# 测试覆盖率
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Maven
.mvn/
mvnw
mvnw.cmd

# Gradle
.gradle
gradle/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Eclipse
.project
.classpath
.c9/
*.launch
.settings/
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs
hs_err_pid*

# Spring Boot
application-*.yml
!application-druid.yml

# 上传文件
upload/

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 备份文件
*.bak
*.backup
*.old

# 临时文件
*.tmp
*.temp
