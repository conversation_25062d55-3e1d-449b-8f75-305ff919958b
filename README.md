# 科研成果多维敏捷管控中心

## 项目简介

科研成果多维敏捷管控中心是基于敏捷平台的科研成果管理服务平台，旨在构建学校科研成果量化管理服务平台应用。通过流程引擎、低代码工具创建科研服务，丰富平台的应用中心，为学校各个部门、院系提供更便捷的科研应用服务。

## 核心功能

### 🏠 敏捷支撑平台
- **融合门户**：个人工作台，支持PC端
- **通知公告**：全文检索、分类管理、权限控制
- **待办事项**：任务分配、状态管理、提醒功能
- **站内消息**：消息发送、接收、分类管理
- **通讯录**：机构通讯录管理和查看
- **身份认证**：统一身份认证、多种认证方式
- **权限管理**：批量授权、复制权限
- **流程引擎**：可视化流程设计、版本管理、监控


### 🏢 基础设置
- **部门管理**：部门信息维护、层级结构管理
- **岗位管理**：岗位信息维护、分类管理
- **人员管理**：人员信息管理、分类管理
- **个人信息**：个人信息维护、审批流程

### 📊 项目管理
- **纵向项目**：立项、变更、结项全生命周期管理
- **横向项目**：合同管理、合作单位管理
- **校级项目**：校级项目管理、成果展示
- **教学项目**：教学项目管理、分类统计

### 📝 申报评审
- **网上申报**：申报计划、条件设定、材料提交
- **网上评审**：评审方案、专家指派、结果汇总

### 🔬 科研服务
- **科研数据仓库**：数据采集、标准化、分类管理
- **科研成果认定**：成果申报、审核、认定管理
- **职称评审**：评审计划、报名、流程管理

### 🎓 龙湖讲坛
- **讲坛管理**：申请审批、信息管理、资源管理
- **讲坛展示**：专题网站、资源播放、观看统计

### 📈 高级功能
- **统计分析**：数据统计、可视化图表、态势感知
- **AI功能**：文档识别、报告生成、智能推荐
- **系统监控**：日志收集、性能监控、安全防护

## 技术架构

### 后端技术栈
- **框架**：Spring Boot 2.7.x
- **安全**：Spring Security 5.7.x
- **数据库**：MySQL 8.0+ / MyBatis Plus 3.5.x
- **缓存**：Redis 6.0+
- **工作流**：Activiti 7.x
- **文档**：Swagger 3.x
- **任务调度**：Quartz 2.3.x

### 前端技术栈
- **框架**：Vue 3.2.x + TypeScript 4.7.x
- **构建工具**：Vite 3.x
- **UI组件**：Element Plus 2.2.x
- **状态管理**：Pinia 2.0.x
- **路由**：Vue Router 4.x
- **HTTP客户端**：Axios 0.27.x
- **图表**：ECharts 5.x

### 系统要求
- **用户规模**：支持10000+注册用户
- **并发能力**：支持300+同时在线用户
- **响应时间**：一般页面<5秒，复杂查询<10秒
- **兼容性**：支持国产化软硬件环境

## 项目结构

```
admin-new/
├── backend/                 # 后端项目 (Spring Boot)
│   ├── src/main/java/
│   │   └── com/research/
│   │       ├── common/      # 公共模块
│   │       ├── framework/   # 框架模块
│   │       ├── system/      # 系统管理
│   │       ├── portal/      # 门户模块
│   │       ├── workflow/    # 工作流模块
│   │       ├── project/     # 项目管理
│   │       ├── research/    # 科研管理
│   │       └── forum/       # 龙湖讲坛
│   └── src/main/resources/
├── frontend/                # 前端项目 (Vue 3)
│   ├── src/
│   │   ├── api/            # API接口
│   │   ├── components/     # 公共组件
│   │   ├── views/          # 页面视图
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   └── utils/          # 工具类
│   └── package.json
├── sql/                     # 数据库脚本
├── docs/                    # 项目文档
├── scripts/                 # 脚本文件
└── docker/                  # Docker配置
```

## 开发计划

### 第一阶段：基础平台搭建 (1-2周)
- [x] 项目架构搭建
- [ ] 基础系统管理功能
- [ ] 统一身份认证
- [ ] 基础CRUD框架

### 第二阶段：核心业务功能 (2-3周)
- [ ] 个人工作台
- [ ] 通知公告管理
- [ ] 待办事项管理
- [ ] 站内消息管理

### 第三阶段：工作流引擎 (2-3周)
- [ ] 工作流基础功能
- [ ] 流程设计器
- [ ] 流程监控管理

### 第四阶段：项目管理功能 (3-4周)
- [ ] 纵向项目管理
- [ ] 横向项目管理
- [ ] 校级项目管理
- [ ] 教学项目管理

### 第五阶段：申报评审功能 (2-3周)
- [ ] 网上申报管理
- [ ] 网上评审功能

### 第六阶段：科研服务功能 (2-3周)
- [ ] 科研数据仓库
- [ ] 科研成果认定
- [ ] 职称评审

### 第七阶段：龙湖讲坛 (1-2周)
- [ ] 讲坛管理
- [ ] 讲坛展示

### 第八阶段：高级功能 (2-3周)
- [ ] 统计分析
- [ ] AI功能集成

### 第九阶段：系统优化 (1-2周)
- [ ] 性能优化
- [ ] 安全加固
- [ ] 测试部署

## 快速开始

### 环境要求
- **Java**: JDK 8 或 JDK 11
- **Node.js**: 16.x LTS
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Maven**: 3.8.x

### 数据库初始化
```bash
# 执行数据库脚本
mysql -u root -p < sql/research_db_schema.sql
mysql -u root -p < sql/research_db_init_data.sql
```

### 后端启动
```bash
# 进入后端目录
cd backend

# 安装依赖并启动
mvn clean install
mvn spring-boot:run
```

### 前端启动
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 一键启动（推荐）
```bash
# 使用脚本一键启动开发环境
chmod +x scripts/dev.sh
./scripts/dev.sh
```

### Docker部署
```bash
# 使用Docker Compose部署
cd docker
docker-compose up -d
```

## 文档说明

### 📋 需求文档
- [需求分析与开发计划](docs/需求分析与开发计划.md) - 详细的需求分析和开发规划
- [开发TODO清单](docs/开发TODO清单.md) - 完整的开发任务清单

### 🏗️ 设计文档
- [项目结构设计](docs/项目结构设计.md) - 项目结构和模块设计
- [技术选型与架构设计](docs/技术选型与架构设计.md) - 技术选型说明和架构设计

### 📚 开发文档
- [API接口文档](docs/API接口文档.md) - 接口规范和说明
- [数据库设计文档](docs/数据库设计文档.md) - 数据库结构设计

### 🚀 运维文档
- [部署运维文档](docs/部署运维文档.md) - 部署和运维指南
- [用户操作手册](docs/用户操作手册.md) - 用户使用说明

## 开发规范

### 代码规范
- 遵循Java和JavaScript命名规范
- 类、方法、重要逻辑必须有注释
- 统一代码格式，使用IDE格式化工具

### 接口规范
- 遵循RESTful设计原则
- 统一的JSON响应格式
- 统一的错误码和错误信息

### 数据库规范
- 表名、字段名使用下划线命名
- 必要字段如创建时间、更新时间等
- 合理创建索引，提高查询性能

### 安全规范
- 接口级别的权限控制
- 输入数据验证和过滤
- 使用参数化查询防止SQL注入
- 输出数据转义防止XSS攻击

## 贡献指南

### 分支策略
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于新功能开发
- `hotfix/*`: 热修复分支，用于紧急修复

### 提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 代码审查
- 所有代码必须经过代码审查
- 审查重点：功能实现、代码质量、安全性
- 使用Pull Request进行代码审查

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

- **项目负责人**：[姓名]
- **邮箱**：[邮箱地址]
- **项目地址**：[Git仓库地址]

## 更新日志

### v1.0.0 (计划中)
- 基础平台功能
- 系统管理模块
- 门户模块
- 项目管理模块

### v0.1.0 (当前)
- 项目初始化
- 需求分析完成
- 技术选型确定
- 开发计划制定

---

**注意**：本项目目前处于开发阶段，功能持续完善中。如有问题或建议，欢迎提交Issue或Pull Request。
